import React, { useEffect, useRef } from 'react';

import DButton from '../../Global/DButton';
import DInput from '../../Global/DInput/DInput';
import DInputBlock from '../../Global/DInput/DInputBlock';
import ChangePassword from '../ChangePassword';
import {
  Transition,
} from '@headlessui/react';
import Form2FA from '../2FA';
import DProfileImage from '@/components/DProfileImage';
import Disable2FA from '../Disble2FA';
import useTeamManagementStore from '@/stores/teamManagement/teamManagementStore';

const ProfileForm = ({ 
  user,
  defaultImages, 
  defaultTeamImages,
  userData, 
  setUserData,
  profileImageFile,
  setAccountImage,
  profileImageUrl,
  setAccountImageUrl,
  teamProfileImage,
  setAccountTeamImage,
  teamProfileImageUrl,
  setAccountTeamImageUrl,
  showChangePassword,
  setShowChangePassword,
  showEnable2FA,
  setShowEnable2FA,
  showDisable2FA,
  setShowDisable2FA,
  modifiedData,
  setModifiedData,
  errors,
  setErrors
}) => {
  const { selectedTeam } = useTeamManagementStore((state) => state);
  const accountImageRef = useRef(null);
  const accountTeamLogoRef = useRef(null);

  const handleChangeInput = (e) => {
    const { name, value } = e.target;
    setUserData({ ...userData, [name]: value });
    setModifiedData({ ...modifiedData, [name]: value });
  };

  const setImageError = (name, error) => {
    setErrors({ ...errors, [name]: error });
  };

  const handleImageChange = (name, file, ...rest) => {
    if (file instanceof File) {
      if (name === 'profile_image') {
        setAccountImage(file);
        setAccountImageUrl(URL.createObjectURL(file));
      } else {
        setAccountTeamImage(file);
        setAccountTeamImageUrl(URL.createObjectURL(file));
      }
    } else {
      if (name === 'profile_image') {
        setAccountImage(null);
        setAccountImageUrl(file);
      } else {
        setAccountTeamImage(null);
        setAccountTeamImageUrl(file);
      }
    }

    setModifiedData({ ...modifiedData, [name]: file });
  };

  useEffect(() => {
    const tempUSer = {
      name: `${user?.first_name} ${user?.last_name}`,
      ...user,
    };

    setUserData(tempUSer);

    return () => {};
  }, [user, setUserData]);

  useEffect(() => {
    if (defaultImages?.results?.length > 0 && !user?.profile_image) {
      setAccountImageUrl(defaultImages?.results[0]?.big_image_url);
    }
    if (defaultTeamImages?.results?.length > 0 && !user?.team_key?.team_icon) {
      setAccountTeamImageUrl(defaultTeamImages?.results[0]?.big_image_url);
    }
  }, [defaultImages, defaultTeamImages, user?.profile_image, user?.team_key?.team_icon, setAccountImageUrl, setAccountTeamImageUrl]);

  return (
    <div className="flex flex-col h-full relative">
        <div className="flex flex-col gap-size5 w-full overflow-y-auto scrollbar">
          <DProfileImage
            isRounded
            label="Account image"
            name="profile_image"
            imageUrl={profileImageUrl}
            defaultImages={defaultImages?.results}
            handleImageChange={(file) => handleImageChange('profile_image', file)}
            imageFile={profileImageFile}
            setImageError={setImageError}
            error={errors?.profile_image}
            maxSizeFile={150 * 1024}
            required
          />
      <DInputBlock
        label="Name"
        description="Feel free to set it as first name, full name or nickname."
      >
        <DInput
          type="text"
          value={userData?.full_name || ''}
          placeholder="Click to add"
          name="full_name"
          onChange={handleChangeInput}
          error={errors?.full_name}
        />
      </DInputBlock>
      <DInputBlock
        label="Email"
        alert="If changed email confirmation will be required."
      >
        <DInput
          type="text"
          value={userData?.email}
          name="email"
          placeholder="Click to add"
          disabled
        />
      </DInputBlock>
      <DInputBlock label="Password" alert="If changed you will be signed out.">
        {!showChangePassword && (
          <DButton
            variant="outlined"
            size="sm"
            className="!w-max"
            fullWidth
            onClick={() => setShowChangePassword(true)}
          >
            Change password
          </DButton>
        )}
        <Transition show={showChangePassword || false}>
          <div className="w-full">
            <ChangePassword onClose={() => setShowChangePassword(false)} />
          </div>
        </Transition>
      </DInputBlock>
      <DInputBlock
        label="2FA"
        alert="You will need an authenticator mobile app to setup 2FA."
      >
        {!showEnable2FA && !userData?.requires_2FA && (
          <DButton
            variant="outlined"
            size="sm"
            className="!w-max"
            fullWidth
            onClick={() => setShowEnable2FA(true)}
          >
            Enable 2FA
          </DButton>
        )}
        <Transition show={showEnable2FA || false}>
          <div className="w-full">
            <Form2FA
              onClose={() => setShowEnable2FA(false)}
              user={user}
              setUser={setUserData}
            />
          </div>
        </Transition>
        {userData?.requires_2FA && !showDisable2FA && (
          <DButton
            variant="outlined"
            size="sm"
            className="!w-max"
            fullWidth
            onClick={() => setShowDisable2FA(true)}
          >
            Disable 2FA
          </DButton>
        )}
        <Transition show={showDisable2FA || false}>
          <div className="w-full">
            <Disable2FA
              onClose={() => setShowDisable2FA(false)}
              user={user}
              setUser={setUserData}
            />
          </div>
        </Transition>
      </DInputBlock>
      <div className="w-full h-px bg-grey-5"></div>

      {(!selectedTeam || selectedTeam?.owner_id === user?.id) && (
        <DInputBlock label="Team name" note="">
          <DInput
            type="text"
            value={userData?.team_key?.team_name || ''}
            placeholder="Click to add"
            onChange={(e) => {
              setUserData({
                ...userData,
                team_key: {
                  ...userData?.team_key,
                  team_name: e.target.value,
                },
              });
              setModifiedData({
                ...modifiedData,
                team_name: e.target.value,
              });
            }}
          />
        </DInputBlock>
      )}
      {(!selectedTeam || selectedTeam?.owner_id === user?.id) && (
        <DProfileImage
          label="Team icon"
          name="team_profile_image"
          imageUrl={teamProfileImageUrl}
          defaultImages={defaultTeamImages?.results}
          handleImageChange={(file) =>
            handleImageChange('team_profile_image', file)
          }
          imageFile={teamProfileImage}
          setImageError={setImageError}
          error={errors?.team_profile_image}
          maxSizeFile={150 * 1024}
          required
        />
      )}
        </div>
    </div>
  );
};

export default ProfileForm;
